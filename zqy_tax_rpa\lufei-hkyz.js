const { getherInvoice, downloadEXCEL, downloadPDF, getPDF, extractZIP, uploadPDF } = require('./Download.js')
const pbottleRPA = require("./pbottleRPA");
const { spawn } = require('child_process');
const Untils = require("./untils");
const untils = new Untils();

main()

async function main(){
    pbottleRPA.browserCMD_click(`button span:contains(登录)`)
    const slider = await untils.waitImage('/input/1920/slider.png')
    pbottleRPA.moveMouseSmooth(slider.x, slider.y)
    pbottleRPA.moveMouseSmooth(slider.x + 225, slider.y + 40)
    const img = await refresh()
    // console.log('res.target', img.target)
    // console.log('res.back', img.back)
    const res = await untils.getRotateDistance(img.target,img.back)
    console.log(`res: ${JSON.stringify(res.data.rotation_angle)}`)
    const distance  = res.data.rotation_angle
    console.log(`四舍五入之后的distance: `,Math.round(distance * 242 / 360))
    pbottleRPA.moveMouseSmooth(slider.x, slider.y)
    pbottleRPA.mouseLeftDragTo(slider.x + Math.round(distance * 242 / 360) , slider.y)
    pbottleRPA.sleep(2000)
    const globalLogMsg = await pbottleRPA.browserCMD_text(`body > div.el-message.el-message--warning.el-icon-warning-color > p`)
    console.log(`globalLogMsg: ${globalLogMsg}`)
    
    async function refresh(){
        pbottleRPA.sleep(1000)
        let target = await pbottleRPA.browserCMD_attr('div#tpass-captcha-slider-img-div img', 'src')
        let back = await pbottleRPA.browserCMD_attr('div[class="bg-img-div"] img', 'src')
        if(target == 'ok' || back == 'ok'){
            pbottleRPA.mouseClick()
            return await refresh();
        }else{
            return {target,back}
        }
    } 
}

