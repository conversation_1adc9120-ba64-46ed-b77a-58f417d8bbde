// 测试顺序执行的简化版本
const fs = require('fs');
const path = require('path');

// 模拟函数
const mockExtractZIP = () => {
    return new Promise((resolve) => {
        console.log('extractZIP() 开始执行');
        setTimeout(() => {
            console.log('extractZIP() 执行完成');
            resolve();
        }, 2000); // 模拟2秒的处理时间
    });
};

const mockUploadPDF = () => {
    return new Promise((resolve) => {
        console.log('uploadPDF() 开始执行');
        setTimeout(() => {
            console.log('uploadPDF() 执行完成');
            resolve();
        }, 1500); // 模拟1.5秒的处理时间
    });
};

// 测试顺序执行
const testSequentialExecution = async () => {
    console.log('=== 测试顺序执行 ===');
    console.log('开始时间:', new Date().toLocaleTimeString());
    
    try {
        // 先执行 extractZIP
        await mockExtractZIP();
        
        // 再执行 uploadPDF
        await mockUploadPDF();
        
        console.log('所有操作完成');
        console.log('结束时间:', new Date().toLocaleTimeString());
    } catch (error) {
        console.error('执行过程中出错:', error);
    }
};

// 测试并行执行（对比）
const testParallelExecution = async () => {
    console.log('\n=== 测试并行执行（对比） ===');
    console.log('开始时间:', new Date().toLocaleTimeString());
    
    try {
        // 并行执行
        await Promise.all([
            mockExtractZIP(),
            mockUploadPDF()
        ]);
        
        console.log('所有操作完成');
        console.log('结束时间:', new Date().toLocaleTimeString());
    } catch (error) {
        console.error('执行过程中出错:', error);
    }
};

// 运行测试
const runTests = async () => {
    await testSequentialExecution();
    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    await testParallelExecution();
};

runTests().catch(console.error);
