const pbottleRPA = require("./pbottleRPA");
const fs = require('fs');
const xlsx = require("node-xlsx");
const workSheetsFromFile = xlsx.parse(`${__dirname}\\采购寻源智能体.xlsx`);
const workSheet = workSheetsFromFile[0].data;

const dataLMN = [];
for (let i = 1; i < workSheet.length; i++) {
    const row = workSheet[i];
    // 假设L M N列是第12、13、14列，索引分别是11、12、13
    const l = row[11];
    const m = row[12];
    const n = row[13];
    // 将每一行的数据存储为一个数组
dataLMN.push({
        code: l,
        name: m,
        category: n
    });
}
// 将数据写入一个新的JavaScript文件
const dataString = `const GOODS_CODE_Table = ${JSON.stringify(dataLMN)};`;
fs.writeFile('goods-code-table.js', dataString, (err) => {
    if (err) throw err;
    console.log('数据已成功写入goods-code-table.js文件');
});


